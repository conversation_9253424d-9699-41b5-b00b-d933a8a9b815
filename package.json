{"name": "marketing-agency", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "set ANALYZE=true && next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-slot": "^1.2.3", "@tsparticles/engine": "^3.9.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^12.1.0", "firebase-admin": "^13.4.0", "lucide-react": "^0.542.0", "next": "15.5.2", "next-intl": "^4.3.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.5.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}